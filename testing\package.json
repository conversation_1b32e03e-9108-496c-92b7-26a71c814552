{"name": "atma-backend-testing", "version": "1.0.0", "description": "AI-Driven Talent Mapping Assessment Backend Testing Suite", "main": "e2e-load-test.js", "scripts": {}, "keywords": ["testing", "e2e", "load-testing", "api-testing", "atma", "backend", "websocket"], "author": "ATMA Development Team", "license": "ISC", "dependencies": {"axios": "^1.10.0", "socket.io-client": "^4.7.5", "uuid": "^11.1.0"}, "engines": {"node": ">=16.0.0"}}